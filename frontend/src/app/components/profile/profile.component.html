<!-- Page de profil moderne -->
<div class="profile-page">
  <!-- Header de la page -->
  <div class="profile-header">
    <div class="header-content">
      <div class="user-avatar">
        <span class="avatar-icon">👤</span>
      </div>
      <div class="header-info">
        <h1 class="page-title">Mon Profil</h1>
        <p class="page-subtitle">Gérez vos informations personnelles et paramètres de compte</p>
      </div>
    </div>
    <div class="header-actions">
      <div class="user-status" [class.online]="isOnline" [class.offline]="!isOnline">
        <span class="status-dot"></span>
        <span class="status-text">{{ isOnline ? 'En ligne' : 'Hors ligne' }}</span>
      </div>
    </div>
  </div>

  <!-- Contenu principal -->
  <div class="profile-content">
    
    <!-- Carte d'informations utilisateur -->
    <div class="user-info-card">
      <div class="card-header">
        <h2 class="card-title">
          <span class="card-icon">📋</span>
          Informations du compte
        </h2>
      </div>
      <div class="card-content">
        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">
              <span class="label-icon">👤</span>
              Nom d'utilisateur
            </div>
            <div class="info-value">{{ currentUser?.username }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <span class="label-icon">📧</span>
              Adresse e-mail
            </div>
            <div class="info-value">{{ currentUser?.email }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <span class="label-icon">🏷️</span>
              Rôle
            </div>
            <div class="info-value">
              <span class="role-badge" [class]="getRoleClass(currentUser?.role)">
                {{ getRoleLabel(currentUser?.role) }}
              </span>
            </div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <span class="label-icon">📅</span>
              Membre depuis
            </div>
            <div class="info-value">{{ formatDate(currentUser?.createdAt) }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Formulaire de modification -->
    <div class="edit-profile-card">
      <div class="card-header">
        <h2 class="card-title">
          <span class="card-icon">✏️</span>
          Modifier mes informations
        </h2>
      </div>
      <div class="card-content">
        <form [formGroup]="profileForm" (ngSubmit)="onSubmit()" class="profile-form">
          
          <!-- Section Informations personnelles -->
          <div class="form-section">
            <div class="section-header">
              <span class="section-icon">👤</span>
              <h3 class="section-title">Informations personnelles</h3>
            </div>
            
            <div class="form-grid">
              <div class="form-field">
                <label for="firstName" class="field-label">
                  <span class="label-icon">🏷️</span>
                  Prénom
                </label>
                <input 
                  id="firstName"
                  type="text" 
                  formControlName="firstName" 
                  class="field-input"
                  placeholder="Votre prénom"
                  required>
                <div class="field-error" *ngIf="getFieldError('firstName')">
                  {{ getFieldError('firstName') }}
                </div>
              </div>

              <div class="form-field">
                <label for="lastName" class="field-label">
                  <span class="label-icon">👥</span>
                  Nom de famille
                </label>
                <input 
                  id="lastName"
                  type="text" 
                  formControlName="lastName" 
                  class="field-input"
                  placeholder="Votre nom de famille"
                  required>
                <div class="field-error" *ngIf="getFieldError('lastName')">
                  {{ getFieldError('lastName') }}
                </div>
              </div>
            </div>

            <div class="form-field full-width">
              <label for="phone" class="field-label">
                <span class="label-icon">📱</span>
                Numéro de téléphone
              </label>
              <input 
                id="phone"
                type="tel" 
                formControlName="phone" 
                class="field-input"
                placeholder="+216 XX XXX XXX">
              <div class="field-error" *ngIf="getFieldError('phone')">
                {{ getFieldError('phone') }}
              </div>
            </div>
          </div>

          <!-- Section Adresse -->
          <div class="form-section">
            <div class="section-header">
              <span class="section-icon">📍</span>
              <h3 class="section-title">Adresse</h3>
            </div>
            
            <div class="form-field full-width">
              <label for="address" class="field-label">
                <span class="label-icon">🏠</span>
                Adresse complète
              </label>
              <textarea 
                id="address"
                formControlName="address" 
                class="field-textarea"
                placeholder="Votre adresse complète"
                rows="3"></textarea>
              <div class="field-error" *ngIf="getFieldError('address')">
                {{ getFieldError('address') }}
              </div>
            </div>
          </div>

          <!-- Actions -->
          <div class="form-actions">
            <button 
              type="button" 
              class="btn btn-secondary" 
              (click)="resetForm()"
              [disabled]="isLoading">
              <span class="btn-icon">🔄</span>
              Annuler les modifications
            </button>
            
            <button 
              type="submit" 
              class="btn btn-primary" 
              [disabled]="profileForm.invalid || isLoading">
              <span *ngIf="isLoading" class="btn-loading">
                <span class="spinner">🔄</span>
                Sauvegarde...
              </span>
              <span *ngIf="!isLoading" class="btn-content">
                <span class="btn-icon">💾</span>
                Sauvegarder les modifications
              </span>
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Carte de sécurité -->
    <div class="security-card">
      <div class="card-header">
        <h2 class="card-title">
          <span class="card-icon">🔐</span>
          Sécurité du compte
        </h2>
      </div>
      <div class="card-content">
        <div class="security-actions">
          <button class="security-btn" (click)="openChangePasswordModal()">
            <span class="security-icon">🔑</span>
            <div class="security-info">
              <div class="security-title">Changer le mot de passe</div>
              <div class="security-desc">Modifiez votre mot de passe pour sécuriser votre compte</div>
            </div>
            <span class="security-arrow">→</span>
          </button>
          
        
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modale de changement de mot de passe -->
<div *ngIf="showChangePasswordModal" class="password-modal-overlay" (click)="closeChangePasswordModal()">
  <div class="password-modal" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h3 class="modal-title">
        <span class="modal-icon">🔑</span>
        Changer le mot de passe
      </h3>
      <button class="close-btn" (click)="closeChangePasswordModal()">
        <span class="close-icon">✕</span>
      </button>
    </div>
    
    <div class="modal-content">
      <form [formGroup]="passwordForm" (ngSubmit)="onChangePassword()" class="password-form">
        <div class="form-field">
          <label for="currentPassword" class="field-label">
            <span class="label-icon">🔒</span>
            Mot de passe actuel
          </label>
          <input 
            id="currentPassword"
            type="password" 
            formControlName="currentPassword" 
            class="field-input"
            placeholder="Votre mot de passe actuel"
            required>
          <div class="field-error" *ngIf="getPasswordFieldError('currentPassword')">
            {{ getPasswordFieldError('currentPassword') }}
          </div>
        </div>

        <div class="form-field">
          <label for="newPassword" class="field-label">
            <span class="label-icon">🔑</span>
            Nouveau mot de passe
          </label>
          <input 
            id="newPassword"
            type="password" 
            formControlName="newPassword" 
            class="field-input"
            placeholder="Votre nouveau mot de passe"
            required>
          <div class="field-error" *ngIf="getPasswordFieldError('newPassword')">
            {{ getPasswordFieldError('newPassword') }}
          </div>
        </div>

        <div class="form-field">
          <label for="confirmPassword" class="field-label">
            <span class="label-icon">🔓</span>
            Confirmer le nouveau mot de passe
          </label>
          <input 
            id="confirmPassword"
            type="password" 
            formControlName="confirmPassword" 
            class="field-input"
            placeholder="Confirmez votre nouveau mot de passe"
            required>
          <div class="field-error" *ngIf="getPasswordFieldError('confirmPassword')">
            {{ getPasswordFieldError('confirmPassword') }}
          </div>
        </div>

        <div class="modal-actions">
          <button 
            type="button" 
            class="btn btn-secondary" 
            (click)="closeChangePasswordModal()"
            [disabled]="isChangingPassword">
            <span class="btn-icon">❌</span>
            Annuler
          </button>
          
          <button 
            type="submit" 
            class="btn btn-primary" 
            [disabled]="passwordForm.invalid || isChangingPassword">
            <span *ngIf="isChangingPassword" class="btn-loading">
              <span class="spinner">🔄</span>
              Modification...
            </span>
            <span *ngIf="!isChangingPassword" class="btn-content">
              <span class="btn-icon">🔑</span>
              Changer le mot de passe
            </span>
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
