import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule, AbstractControl } from '@angular/forms';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { AuthService } from '../../services/auth.service';
import { User } from '../../models/user.model';

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatSnackBarModule
  ],
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.scss']
})
export class ProfileComponent implements OnInit {
  profileForm: FormGroup;
  passwordForm: FormGroup;
  currentUser: User | null = null;
  isLoading = false;
  isChangingPassword = false;
  showChangePasswordModal = false;
  isOnline = true;

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private snackBar: MatSnackBar,
    private cdr: ChangeDetectorRef
  ) {
    this.profileForm = this.fb.group({
      firstName: ['', [Validators.required, Validators.minLength(2)]],
      lastName: ['', [Validators.required, Validators.minLength(2)]],
      phone: ['', [Validators.pattern(/^[0-9+\-\s()]+$/)]],
      address: ['']
    });

    this.passwordForm = this.fb.group({
      currentPassword: ['', [Validators.required]],
      newPassword: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', [Validators.required]]
    }, { validators: this.passwordMatchValidator });
  }

  ngOnInit(): void {
    this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
      if (user) {
        this.profileForm.patchValue({
          firstName: user.firstName,
          lastName: user.lastName,
          phone: user.phone || '',
          address: user.address || ''
        });
      }
    });
  }

  passwordMatchValidator(control: AbstractControl): { [key: string]: any } | null {
    const newPassword = control.get('newPassword');
    const confirmPassword = control.get('confirmPassword');

    if (newPassword && confirmPassword && newPassword.value !== confirmPassword.value) {
      return { passwordMismatch: true };
    }
    return null;
  }

  onSubmit(): void {
    if (this.profileForm.valid && this.currentUser) {
      this.isLoading = true;

      const updateData = {
        firstName: this.profileForm.value.firstName,
        lastName: this.profileForm.value.lastName,
        phone: this.profileForm.value.phone
      };

      // Utiliser le service auth pour mettre à jour le profil
      this.authService.updateUserProfile(updateData).subscribe({
        next: (updatedUser) => {
          console.log('Profil mis à jour avec succès:', updatedUser);

          // Mettre à jour l'utilisateur actuel localement
          this.currentUser = updatedUser;

          this.isLoading = false;
          this.snackBar.open('Profil mis à jour avec succès !', 'Fermer', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
        },
        error: (error) => {
          console.error('Erreur lors de la mise à jour du profil:', error);
          this.isLoading = false;
          this.snackBar.open('Erreur lors de la mise à jour du profil', 'Fermer', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      });
    }
  }

  resetForm(): void {
    if (this.currentUser) {
      this.profileForm.patchValue({
        firstName: this.currentUser.firstName,
        lastName: this.currentUser.lastName,
        phone: this.currentUser.phone || '',
        address: this.currentUser.address || ''
      });
    }
  }

  openChangePasswordModal(): void {
    this.showChangePasswordModal = true;
    this.passwordForm.reset();
    this.cdr.detectChanges();
  }

  closeChangePasswordModal(): void {
    this.showChangePasswordModal = false;
    this.passwordForm.reset();
    this.cdr.detectChanges();
  }

  onChangePassword(): void {
    if (this.passwordForm.valid && this.currentUser) {
      this.isChangingPassword = true;

      const passwordData = {
        currentPassword: this.passwordForm.value.currentPassword,
        newPassword: this.passwordForm.value.newPassword
      };

      // Simuler l'appel API pour changer le mot de passe
      // Dans une vraie application, cela appellerait un service
      setTimeout(() => {
        this.isChangingPassword = false;
        this.showChangePasswordModal = false;
        this.passwordForm.reset();

        this.snackBar.open('Mot de passe modifié avec succès !', 'Fermer', {
          duration: 3000,
          panelClass: ['success-snackbar']
        });
      }, 2000);
    }
  }

  showAccountInfo(): void {
    this.snackBar.open('Fonctionnalité en cours de développement', 'Fermer', {
      duration: 3000,
      panelClass: ['info-snackbar']
    });
  }

  getRoleLabel(role?: string): string {
    switch (role) {
      case 'PATIENT': return 'Patient';
      case 'NURSE': return 'Infirmier';
      case 'ADMIN': return 'Administrateur';
      default: return '';
    }
  }

  getRoleClass(role?: string): string {
    switch (role) {
      case 'PATIENT': return 'patient';
      case 'NURSE': return 'nurse';
      case 'ADMIN': return 'admin';
      default: return '';
    }
  }

  formatDate(date?: Date | string): string {
    if (!date) return 'Non disponible';
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  getFieldError(fieldName: string): string {
    const field = this.profileForm.get(fieldName);
    if (field && field.errors && field.touched) {
      if (field.errors['required']) {
        return 'Ce champ est requis';
      }
      if (field.errors['minlength']) {
        return `Minimum ${field.errors['minlength'].requiredLength} caractères`;
      }
      if (field.errors['pattern']) {
        return 'Format invalide';
      }
    }
    return '';
  }

  getPasswordFieldError(fieldName: string): string {
    const field = this.passwordForm.get(fieldName);
    if (field && field.errors && field.touched) {
      if (field.errors['required']) {
        return 'Ce champ est requis';
      }
      if (field.errors['minlength']) {
        return `Minimum ${field.errors['minlength'].requiredLength} caractères`;
      }
    }

    // Vérifier l'erreur de correspondance des mots de passe
    if (fieldName === 'confirmPassword' && this.passwordForm.errors?.['passwordMismatch'] && field?.touched) {
      return 'Les mots de passe ne correspondent pas';
    }

    return '';
  }
}
